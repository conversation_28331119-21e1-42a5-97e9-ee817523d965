{"roles": [{"id": "admin", "name": "Admin", "description": "Full administrative access and management capabilities", "color": "bg-red-500", "access_level": "100% access"}, {"id": "supervisor", "name": "Supervisor", "description": "Department and advanced ticket handling operations", "color": "bg-blue-500", "access_level": "90% access"}, {"id": "agent", "name": "Agent", "description": "Customer support and ticket handling", "color": "bg-green-500", "access_level": "70% access"}, {"id": "viewer", "name": "Viewer", "description": "Read-only access to tickets and reports", "color": "bg-gray-500", "access_level": "5% access"}], "actions": [{"id": "assign_ticket", "name": "Assign <PERSON>", "description": "Assign tickets to team members", "category": "Ticket Management", "riskLevel": "low"}, {"id": "transfer_ticket", "name": "Transfer Ticket", "description": "Transfer tickets between departments", "category": "Ticket Management", "riskLevel": "medium"}, {"id": "close_ticket", "name": "Close Ticket", "description": "Mark tickets as resolved and close them", "category": "Ticket Management", "riskLevel": "low"}, {"id": "delete_ticket", "name": "Delete Ticket", "description": "Permanently delete tickets from the system", "category": "Ticket Management", "riskLevel": "high"}, {"id": "edit_user_data", "name": "Edit User Data", "description": "Modify internal user information and profiles", "category": "Data Management", "riskLevel": "medium"}, {"id": "edit_customer_data", "name": "Edit Customer Data", "description": "Update customer information and contact details", "category": "Data Management", "riskLevel": "medium"}, {"id": "delete_customer_data", "name": "Delete Customer Data", "description": "Remove customer records from the system", "category": "Data Management", "riskLevel": "high"}, {"id": "export_chat_logs", "name": "Export Chat Logs", "description": "Download chat conversation histories", "category": "Reporting & Export", "riskLevel": "medium"}, {"id": "export_data", "name": "Export Data", "description": "Export system data and reports", "category": "Reporting & Export", "riskLevel": "medium"}, {"id": "view_analytics", "name": "View Analytics", "description": "Access operational metrics and data insights", "category": "Reporting & Export", "riskLevel": "low"}, {"id": "modify_company_profile", "name": "Modify Company Profile", "description": "Update company information and branding", "category": "System Configuration", "riskLevel": "high"}, {"id": "modify_business_hours", "name": "Modify Business Hours", "description": "Set operational hours and availability", "category": "System Configuration", "riskLevel": "medium"}, {"id": "manage_integrations", "name": "Manage Integrations", "description": "Configure third-party service connections", "category": "System Configuration", "riskLevel": "high"}, {"id": "create_users", "name": "Create Users", "description": "Add new team members to the system", "category": "User Management", "riskLevel": "medium"}, {"id": "manage_roles", "name": "Manage Roles", "description": "Create and modify user roles and permissions", "category": "User Management", "riskLevel": "high"}, {"id": "deactivate_users", "name": "Deactivate Users", "description": "Suspend or remove user access", "category": "User Management", "riskLevel": "high"}], "defaultPermissions": {"admin": {"assign_ticket": true, "transfer_ticket": true, "close_ticket": true, "delete_ticket": true, "edit_user_data": true, "edit_customer_data": true, "delete_customer_data": true, "export_chat_logs": true, "export_data": true, "view_analytics": true, "modify_company_profile": true, "modify_business_hours": true, "manage_integrations": true, "create_users": true, "manage_roles": true, "deactivate_users": true}, "supervisor": {"assign_ticket": true, "transfer_ticket": true, "close_ticket": true, "delete_ticket": false, "edit_user_data": true, "edit_customer_data": true, "delete_customer_data": false, "export_chat_logs": true, "export_data": true, "view_analytics": true, "modify_company_profile": false, "modify_business_hours": true, "manage_integrations": false, "create_users": false, "manage_roles": false, "deactivate_users": false}, "agent": {"assign_ticket": true, "transfer_ticket": false, "close_ticket": true, "delete_ticket": false, "edit_user_data": false, "edit_customer_data": true, "delete_customer_data": false, "export_chat_logs": false, "export_data": false, "view_analytics": true, "modify_company_profile": false, "modify_business_hours": false, "manage_integrations": false, "create_users": false, "manage_roles": false, "deactivate_users": false}, "viewer": {"assign_ticket": false, "transfer_ticket": false, "close_ticket": false, "delete_ticket": false, "edit_user_data": false, "edit_customer_data": false, "delete_customer_data": false, "export_chat_logs": false, "export_data": false, "view_analytics": true, "modify_company_profile": false, "modify_business_hours": false, "manage_integrations": false, "create_users": false, "manage_roles": false, "deactivate_users": false}}}