<script lang="ts">
	import { writable } from 'svelte/store';
	import { t } from '$lib/stores/i18n';
	import { Card, Button, Input, Badge, Toggle, Toast } from 'flowbite-svelte';
	import {
		SearchOutline,
		FloppyDiskOutline,
		RefreshOutline,
		ShieldCheckOutline,
		ExclamationCircleOutline,
		CheckCircleOutline,
		CogOutline
	} from 'flowbite-svelte-icons';
	import { fly } from 'svelte/transition';

	// Import RBAC data
	import rbacData from '$src/data/rbac-data.json';

	// Reactive stores
	const permissions = writable(
		rbacData.defaultPermissions as Record<string, Record<string, boolean>>
	);
	const searchTerm = writable('');
	const hasChanges = writable(false);

	// Category tab state
	let activeCategory = '';
	const categoryTabs = Array.from(new Set(rbacData.actions.map((action) => action.category)));

	// Initialize active category to first tab
	$: if (activeCategory === '' && categoryTabs.length > 0) {
		activeCategory = categoryTabs[0];
	}

	// Toast state
	let toastMessage = '';
	let toastStatus = false;
	let toastColor: 'green' | 'blue' | 'red' | 'yellow' = 'green';

	// Derived data
	$: roles = rbacData.roles;
	$: actions = rbacData.actions;

	// Filtered actions based on search and active category tab
	$: filteredActions = actions.filter((action) => {
		const matchesSearch =
			action.name.toLowerCase().includes($searchTerm.toLowerCase()) ||
			action.description.toLowerCase().includes($searchTerm.toLowerCase());
		const matchesCategory = action.category === activeCategory;
		return matchesSearch && matchesCategory;
	});

	function togglePermission(roleId: string, actionId: string) {
		permissions.update((perms) => ({
			...perms,
			[roleId]: {
				...perms[roleId],
				[actionId]: !perms[roleId]?.[actionId]
			}
		}));
		hasChanges.set(true);
	}

	function toggleRolePermissions(roleId: string, grant: boolean) {
		permissions.update((perms) => ({
			...perms,
			[roleId]: Object.fromEntries(actions.map((action) => [action.id, grant]))
		}));
		hasChanges.set(true);
	}

	function resetPermissions() {
		permissions.set(rbacData.defaultPermissions);
		hasChanges.set(false);
		showToast('Permissions reset to default', 'blue');
	}

	function savePermissions() {
		// Here you would typically save to your backend
		console.log('Saving permissions:', $permissions);
		hasChanges.set(false);
		showToast('Permissions saved successfully!', 'green');

		// Dispatch event for parent component
		const event = new CustomEvent('settings-updated', {
			detail: { permissions: $permissions }
		});
		window.dispatchEvent(event);
	}

	function getRolePermissionCount(roleId: string) {
		return Object.values($permissions[roleId] || {}).filter(Boolean).length;
	}

	function getRiskLevelIcon(riskLevel: string) {
		switch (riskLevel) {
			case 'high':
				return ExclamationCircleOutline;
			case 'medium':
				return ShieldCheckOutline;
			default:
				return CheckCircleOutline;
		}
	}

	function getRiskLevelColor(riskLevel: string) {
		switch (riskLevel) {
			case 'high':
				return 'text-red-500';
			case 'medium':
				return 'text-yellow-500';
			default:
				return 'text-green-500';
		}
	}

	function getRiskLevelBadgeColor(riskLevel: string) {
		switch (riskLevel) {
			case 'high':
				return 'red';
			case 'medium':
				return 'yellow';
			default:
				return 'green';
		}
	}

	function showToast(message: string, color: 'green' | 'blue' | 'red' | 'yellow' = 'green') {
		toastMessage = message;
		toastColor = color;
		toastStatus = true;
		setTimeout(() => {
			toastStatus = false;
		}, 3000);
	}

	function handleCategoryTabChange(category: string) {
		activeCategory = category;
	}

	function getCategoryActionCount(category: string) {
		return actions.filter((action) => action.category === category).length;
	}
</script>

{#if toastStatus}
	<Toast
		color={toastColor}
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus
		class="fixed left-3/4 top-1/4 z-50 -translate-x-1/2 -translate-y-1/2 transform"
	>
		<CheckCircleOutline slot="icon" class="h-5 w-5" />
		{toastMessage}
	</Toast>
{/if}

<div class="min-h-screen bg-gray-50 p-6">
	<div class="mx-auto max-w-7xl space-y-6">
		<!-- Header -->
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold text-gray-900">
					{t('role_permissions') || 'Role Permissions'}
				</h1>
				<p class="mt-1 text-gray-600">
					{t('rbac_description') ||
						'Manage access controls and permissions for your customer service platform'}
				</p>
			</div>

			<div class="flex items-center gap-3">
				<Button outline on:click={resetPermissions} disabled={!$hasChanges}>
					<RefreshOutline class="mr-2 h-4 w-4" />
					{t('reset') || 'Reset'}
				</Button>
				<Button
					on:click={savePermissions}
					disabled={!$hasChanges}
					class="bg-blue-600 hover:bg-blue-700"
				>
					<FloppyDiskOutline class="mr-2 h-4 w-4" />
					{t('save_changes') || 'Save Changes'}
				</Button>
			</div>
		</div>

		<!-- Role Overview Cards -->
		<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
			{#each roles as role}
				<Card class="p-4">
					<div class="mb-3 flex items-center gap-3">
						<div class="h-3 w-3 rounded-full {role.color}"></div>
						<h3 class="text-lg font-semibold">{role.name}</h3>
					</div>
					<p class="mb-3 text-sm text-gray-600">{role.description}</p>
					<div class="flex items-center justify-between">
						<Badge color="blue" class="text-xs">
							{getRolePermissionCount(role.id)}/{actions.length}
							{t('permissions') || 'permissions'}
						</Badge>
						<span class="text-sm font-medium text-gray-700">{role.access_level}</span>
					</div>
				</Card>
			{/each}
		</div>

		<!-- Permission Matrix -->
		<Card class="w-full">
			<div class="p-6">
				<div class="mb-6 flex items-center gap-2">
					<CogOutline class="h-5 w-5" />
					<h2 class="text-xl font-semibold">{t('permission_matrix') || 'Permission Matrix'}</h2>
				</div>

				<!-- Search -->
				<div class="mb-6">
					<div class="relative">
						<SearchOutline
							class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400"
						/>
						<Input
							placeholder={t('search_actions') || 'Search actions...'}
							bind:value={$searchTerm}
							class="pl-10"
						/>
					</div>
				</div>

				<!-- Category Tabs -->
				<div class="mb-6">
					<div class="flex flex-wrap gap-1 border-b">
						{#each categoryTabs as category}
							<button
								class="border-b-2 px-4 py-3 text-sm font-medium {activeCategory !== category
									? 'text-gray-500 hover:text-gray-700'
									: ''}"
								style={activeCategory === category ? `color: #3B82F6; border-color: #3B82F6;` : ''}
								on:click={() => handleCategoryTabChange(category)}
							>
								<div class="flex items-center gap-2">
									{category}
									<Badge color="dark" class="text-xs">
										{getCategoryActionCount(category)}
									</Badge>
								</div>
							</button>
						{/each}
					</div>
				</div>

				<!-- Bulk Actions -->
				<div class="mb-6 flex flex-wrap gap-2">
					{#each roles as role}
						<div class="flex items-center gap-2">
							<Button outline size="sm" on:click={() => toggleRolePermissions(role.id, true)}>
								{t('grant_all_to') || 'Grant All to'}
								{role.name}
							</Button>
							<Button outline size="sm" on:click={() => toggleRolePermissions(role.id, false)}>
								{t('revoke_all_from') || 'Revoke All from'}
								{role.name}
							</Button>
						</div>
					{/each}
				</div>

				<!-- Permissions Table -->
				<div class="w-full overflow-x-auto">
					<div class="min-w-full">
						<!-- Table Header -->
						<div
							class="grid gap-4 rounded-t-lg border-b bg-gray-50 p-4 text-sm font-medium"
							style="grid-template-columns: 3fr repeat({roles.length}, 1fr);"
						>
							<div>{t('action') || 'Action'}</div>
							{#each roles as role}
								<div class="text-center">
									<div class="flex items-center justify-center gap-2">
										<div class="h-2 w-2 rounded-full {role.color}"></div>
										{role.name}
									</div>
								</div>
							{/each}
						</div>

						<!-- Table Body - Current Category Actions -->
						{#each filteredActions as action}
							<div
								class="grid gap-4 border-b p-4 last:border-b-0 hover:bg-gray-50"
								style="grid-template-columns: 3fr repeat({roles.length}, 1fr);"
							>
								<div class="space-y-1">
									<div class="flex items-center gap-2">
										<svelte:component
											this={getRiskLevelIcon(action.riskLevel)}
											class="h-4 w-4 {getRiskLevelColor(action.riskLevel)}"
										/>
										<span class="font-medium">{action.name}</span>
										<Badge color={getRiskLevelBadgeColor(action.riskLevel)} class="text-xs">
											{action.riskLevel}
											{t('risk') || 'risk'}
										</Badge>
									</div>
									<p class="text-sm text-gray-600">{action.description}</p>
								</div>

								{#each roles as role}
									<div class="flex justify-center">
										<Toggle
											checked={$permissions[role.id]?.[action.id] || false}
											on:change={() => togglePermission(role.id, action.id)}
											size="small"
										/>
									</div>
								{/each}
							</div>
						{/each}
					</div>
				</div>

				{#if filteredActions.length === 0}
					<div class="py-12 text-center text-gray-500">
						<SearchOutline class="mx-auto mb-4 h-12 w-12 opacity-50" />
						<p>{t('no_actions_found') || 'No actions found matching your search criteria.'}</p>
					</div>
				{/if}
			</div>
		</Card>

		<!-- Changes Indicator -->
		{#if $hasChanges}
			<div class="fixed bottom-6 right-6 z-40 rounded-lg bg-blue-600 p-4 text-white shadow-lg">
				<div class="flex items-center gap-2">
					<ExclamationCircleOutline class="h-4 w-4" />
					<span class="text-sm font-medium"
						>{t('unsaved_changes') || 'You have unsaved changes'}</span
					>
				</div>
			</div>
		{/if}
	</div>
</div>
