"use client"

import { useState, useMemo } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Save, RotateCcw, Shield, AlertTriangle, CheckCircle2, Settings } from "lucide-react"
import { roles, actions, defaultPermissions } from "./data/roles-actions"
import type { PermissionMatrix } from "./types/permissions"
import { RoleCard } from "./components/role-card"
import { PermissionToggle } from "./components/permission-toggle"

export default function PermissionsManager() {
  const [permissions, setPermissions] = useState<PermissionMatrix>(defaultPermissions)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [hasChanges, setHasChanges] = useState(false)

  // Get unique categories
  const categories = useMemo(() => {
    const cats = Array.from(new Set(actions.map((action) => action.category)))
    return ["all", ...cats]
  }, [])

  // Filter actions based on search and category
  const filteredActions = useMemo(() => {
    return actions.filter((action) => {
      const matchesSearch =
        action.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        action.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === "all" || action.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [searchTerm, selectedCategory])

  // Group actions by category for display
  const groupedActions = useMemo(() => {
    const groups: { [category: string]: typeof actions } = {}
    filteredActions.forEach((action) => {
      if (!groups[action.category]) {
        groups[action.category] = []
      }
      groups[action.category].push(action)
    })
    return groups
  }, [filteredActions])

  const togglePermission = (roleId: string, actionId: string) => {
    setPermissions((prev) => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [actionId]: !prev[roleId]?.[actionId],
      },
    }))
    setHasChanges(true)
  }

  const toggleRolePermissions = (roleId: string, grant: boolean) => {
    setPermissions((prev) => ({
      ...prev,
      [roleId]: Object.fromEntries(actions.map((action) => [action.id, grant])),
    }))
    setHasChanges(true)
  }

  const resetPermissions = () => {
    setPermissions(defaultPermissions)
    setHasChanges(false)
  }

  const savePermissions = () => {
    // Here you would typically save to your backend
    console.log("Saving permissions:", permissions)
    setHasChanges(false)
    // Show success message
  }

  const getRolePermissionCount = (roleId: string) => {
    return Object.values(permissions[roleId] || {}).filter(Boolean).length
  }

  const getRiskLevelIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case "high":
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      case "medium":
        return <Shield className="w-4 h-4 text-yellow-500" />
      default:
        return <CheckCircle2 className="w-4 h-4 text-green-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Role Permissions</h1>
            <p className="text-gray-600 mt-1">
              Manage access controls and permissions for your customer service platform
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={resetPermissions} disabled={!hasChanges}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button onClick={savePermissions} disabled={!hasChanges} className="bg-blue-600 hover:bg-blue-700">
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>

        {/* Role Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {roles.map((role) => (
            <RoleCard
              key={role.id}
              role={role}
              permissionCount={getRolePermissionCount(role.id)}
              totalActions={actions.length}
            />
          ))}
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Permission Matrix
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search actions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category === "all" ? "All Categories" : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bulk Actions */}
            <div className="flex flex-wrap gap-2 mb-6">
              {roles.map((role) => (
                <div key={role.id} className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => toggleRolePermissions(role.id, true)}>
                    Grant All to {role.name}
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => toggleRolePermissions(role.id, false)}>
                    Revoke All from {role.name}
                  </Button>
                </div>
              ))}
            </div>

            {/* Permissions Table */}
            <div className="overflow-x-auto">
              <div className="min-w-full">
                {/* Table Header */}
                <div className="grid grid-cols-[2fr,1fr,1fr,1fr,1fr] gap-4 p-4 bg-gray-50 rounded-t-lg border-b font-medium text-sm">
                  <div>Action</div>
                  {roles.map((role) => (
                    <div key={role.id} className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${role.color}`} />
                        {role.name}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Table Body - Grouped by Category */}
                {Object.entries(groupedActions).map(([category, categoryActions]) => (
                  <div key={category} className="border-b">
                    {/* Category Header */}
                    <div className="bg-gray-25 p-3 border-b">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        {category}
                        <Badge variant="secondary" className="text-xs">
                          {categoryActions.length} actions
                        </Badge>
                      </h3>
                    </div>

                    {/* Category Actions */}
                    {categoryActions.map((action) => (
                      <div
                        key={action.id}
                        className="grid grid-cols-[2fr,1fr,1fr,1fr,1fr] gap-4 p-4 hover:bg-gray-50 border-b last:border-b-0"
                      >
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getRiskLevelIcon(action.riskLevel)}
                            <span className="font-medium">{action.name}</span>
                            <Badge
                              variant={
                                action.riskLevel === "high"
                                  ? "destructive"
                                  : action.riskLevel === "medium"
                                    ? "default"
                                    : "secondary"
                              }
                              className="text-xs"
                            >
                              {action.riskLevel} risk
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{action.description}</p>
                        </div>

                        {roles.map((role) => (
                          <div key={role.id} className="flex justify-center">
                            <PermissionToggle
                              granted={permissions[role.id]?.[action.id] || false}
                              onChange={(granted) => togglePermission(role.id, action.id)}
                              riskLevel={action.riskLevel}
                            />
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>

            {filteredActions.length === 0 && (
              <div className="text-center py-12 text-gray-500">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No actions found matching your search criteria.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Changes Indicator */}
        {hasChanges && (
          <div className="fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-lg shadow-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm font-medium">You have unsaved changes</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
